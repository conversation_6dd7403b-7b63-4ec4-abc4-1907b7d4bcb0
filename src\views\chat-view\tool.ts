import { sha256 } from 'js-sha256'
import { CelHiveLinkSplitSymbol } from '@/common/constant'
import { requestPlugin } from '@/common/tools'
import { filterHTML } from './filterHTML'

interface DOMNode {
  index: string
  content: string
  tagName: string
}

export function htmlToIndexedDOM(html: string): DOMNode[] {
  if (!html.includes('<body')) {
    throw new Error('Invalid HTML: Missing <body> tag')
  }

  const parser = new DOMParser()
  const doc = parser.parseFromString(html, 'text/html')
  const result: DOMNode[] = []

  function traverse(node: Element, path: number[] = []) {
    if (node.childNodes.length === 0 || (node.childNodes.length === 1 && node.firstChild?.nodeType === Node.TEXT_NODE)) {
      const textContent = node.textContent?.trim()
      const tagName = node.tagName.toLowerCase()
      const filterTagNames = ['script', 'style']
      if (textContent && textContent.length > 0 && !filterTagNames.includes(tagName)) {
        result.push({
          index: path.join('-'),
          content: textContent,
          tagName,
        })
      }
    }
    else {
      Array.from(node.children).forEach((child, index) => {
        traverse(child, [...path, index])
      })
    }
  }

  Array.from(doc.body.children).forEach((child, index) => {
    traverse(child, [index])
  })

  return result
}

export const getDOMByIndex = (index: string): HTMLElement | undefined => {
  const indexArray = index.split('-').map(Number)
  const body = document.body
  let currentNode = body
  for (const i of indexArray) {
    if (currentNode && currentNode.children.length > i) {
      currentNode = currentNode.children[i] as HTMLElement
    }
    else {
      return undefined
    }
  }
  return currentNode
}

export const extractURL = (str: string) => {
  const regex = /https?:\/\/[^\s<>"]+|www\.[^\s<>"]+/gi
  const urls = str.match(regex) || []

  return {
    urls,
    extractURL: str.replace(regex, '').trim(),
  }
}
const cachedHTMLWithDialogId = (html: string, dialogId: number) => {
  const indexedDBKey = 'celhive-link'
  return new Promise((resolve) => {
    const key = sha256(html + dialogId.toString())
    const request = indexedDB.open(indexedDBKey, 1)
    request.onupgradeneeded = (event) => {
      const db = (event.target as IDBOpenDBRequest).result
      if (!db.objectStoreNames.contains(indexedDBKey)) {
        db.createObjectStore(indexedDBKey, { keyPath: 'key' })
      }
    }
    request.onsuccess = (event) => {
      const db = (event.target as IDBOpenDBRequest).result
      const transaction = db.transaction(indexedDBKey, 'readwrite')
      const store = transaction.objectStore(indexedDBKey)
      store.get(key).onsuccess = (event) => {
        const result = (event.target as IDBRequest).result
        if (result) {
          resolve(true)
        }
        else {
          console.debug(`Caching HTML with dialogId ${dialogId} and key ${key}`)
          store.put({ key })
          resolve(false)
        }
      }
    }
  })
}

export const processPrompt = async (prompt: string, isCelHiveLink: boolean, dialogId: number) => {
  console.log('Processing prompt:', prompt, 'isCelHiveLink:', isCelHiveLink, 'dialogId:', dialogId)
  if (isCelHiveLink) {
    const html = sessionStorage.getItem('celhive-link-html') || ''
    if (html) {
      const cached = await cachedHTMLWithDialogId(html, dialogId)
      if (cached) {
        return prompt
      }
      return `${await filterHTML(html)}\n\n${CelHiveLinkSplitSymbol}${prompt}`
    }
    else {
      return prompt
    }
  }
  else {
    let result = prompt

    const ECP = 'ecp'
    const ECP_SEARCH = 'ecp-search'

    if (result.includes(`/${ECP_SEARCH}`)) {
      try {
        const newPrompt = result.split(`/${ECP_SEARCH}`).filter(Boolean).join('').trim()
        const html = await requestPlugin({ action: ECP_SEARCH, data: { prompt: newPrompt, token: localStorage.token } })
        if (!html) {
          return prompt
        }

        const cached = await cachedHTMLWithDialogId(html, dialogId)
        if (cached) {
          return result.replace(`/${ECP_SEARCH}`, '')
        }

        result = `${await filterHTML(html)}\n\n${CelHiveLinkSplitSymbol}${result.replace(`/${ECP_SEARCH}`, '')}`
      }
      catch (error) {
        console.error('ECP plugin error:', error)
        return prompt
      }
    }
    else if (result.includes(`/${ECP}`)) {
      try {
        const { urls } = extractURL(result)
        const html = await requestPlugin({ action: ECP, data: { urls } })
        if (!html) {
          return prompt
        }

        const cached = await cachedHTMLWithDialogId(html, dialogId)
        if (cached) {
          return result.replace(`/${ECP}`, '')
        }

        result = `${await filterHTML(html)}\n\n${CelHiveLinkSplitSymbol}${result.replace(`/${ECP}`, '')}`
      }
      catch (error) {
        console.error('ECP plugin error:', error)
        return prompt
      }
    }
    return result
  }
}
